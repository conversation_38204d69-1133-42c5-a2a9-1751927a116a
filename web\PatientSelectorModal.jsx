import React, { useState } from 'react';
import './patientSelectorModal.css';
import { ROLE_COLORS } from '../client/src/config/theme';

// This component is deprecated. Use DoctorPatientSelectorModal instead which fetches real patients from Firebase
const SAMPLE_PATIENTS = [];

const PatientSelectorModal = ({ visible, onClose, onSuccess, scannerTitle = 'Select Patient' }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const doctorColors = ROLE_COLORS.doctor;

  // Filter patients based on search query
  const filteredPatients = SAMPLE_PATIENTS.filter(patient =>
    patient.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handlePatientSelect = (patient) => {
    onSuccess(patient);
    onClose();
  };

  const renderPatientItem = (item) => (
    <div
      key={item.id}
      className="patient-item"
      onClick={() => handlePatientSelect(item)}
    >
      <div className="patient-icon" style={{ backgroundColor: '#E8F5E9' }}>
        <i className="icon-person" style={{ color: doctorColors.primary }}>👤</i>
      </div>
      <div className="patient-info">
        <div className="patient-name">{item.name}</div>
        <div className="patient-details">{item.age} years • {item.gender}</div>
      </div>
      <i className="icon-chevron">›</i>
    </div>
  );

  if (!visible) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2 className="modal-title">{scannerTitle}</h2>
          <button className="close-button" onClick={onClose}>
            <i className="icon-close">✕</i>
          </button>
        </div>

        <div className="search-container">
          <i className="search-icon">🔍</i>
          <input
            type="text"
            className="search-input"
            placeholder="Search patients..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {searchQuery && (
            <button className="clear-button" onClick={() => setSearchQuery('')}>
              <i className="icon-clear">⊗</i>
            </button>
          )}
        </div>

        <div className="patient-list">
          {filteredPatients.length > 0 ? (
            filteredPatients.map(renderPatientItem)
          ) : (
            <div className="empty-container">
              <i className="empty-icon">🔍</i>
              <div className="empty-text">No patients found</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PatientSelectorModal;
