.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: #f5f5f5;
}

.icon-close {
  font-size: 24px;
  color: #333;
}

.search-container {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 0 12px;
  margin-bottom: 16px;
}

.search-icon {
  margin-right: 8px;
  color: #757575;
  font-size: 20px;
}

.search-input {
  flex: 1;
  padding: 12px 0;
  font-size: 16px;
  border: none;
  background: transparent;
  outline: none;
}

.search-input::placeholder {
  color: #999;
}

.clear-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: #757575;
}

.icon-clear {
  font-size: 20px;
}

.patient-list {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 16px;
}

.patient-item {
  display: flex;
  align-items: center;
  background-color: white;
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.patient-item:hover {
  background-color: #f9f9f9;
}

.patient-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #E8F5E9;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.icon-person {
  font-size: 24px;
}

.patient-info {
  flex: 1;
}

.patient-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.patient-details {
  font-size: 14px;
  color: #757575;
}

.icon-chevron {
  font-size: 20px;
  color: #9E9E9E;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0;
}

.empty-icon {
  font-size: 48px;
  color: #BDBDBD;
  margin-bottom: 8px;
}

.empty-text {
  font-size: 16px;
  color: #757575;
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 8px;
  }
  
  .modal-content {
    max-height: 90vh;
    padding: 16px;
  }
  
  .modal-title {
    font-size: 16px;
  }
  
  .search-input {
    font-size: 14px;
  }
  
  .patient-name {
    font-size: 15px;
  }
  
  .patient-details {
    font-size: 13px;
  }
}
